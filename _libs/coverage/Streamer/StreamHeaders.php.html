<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Streamer/StreamHeaders.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item"><a href="index.html">Streamer</a></li>
         <li class="breadcrumb-item active">StreamHeaders.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="89.13" aria-valuemin="0" aria-valuemax="100" style="width: 89.13%">
           <span class="sr-only">89.13% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">89.13%</div></td>
       <td class="success small"><div align="right">41&nbsp;/&nbsp;46</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="85.71" aria-valuemin="0" aria-valuemax="100" style="width: 85.71%">
           <span class="sr-only">85.71% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">85.71%</div></td>
       <td class="success small"><div align="right">12&nbsp;/&nbsp;14</div></td>
       <td class="success small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="Nzoom\Export\Streamer\StreamHeaders">StreamHeaders</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="89.13" aria-valuemin="0" aria-valuemax="100" style="width: 89.13%">
           <span class="sr-only">89.13% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">89.13%</div></td>
       <td class="success small"><div align="right">41&nbsp;/&nbsp;46</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="85.71" aria-valuemin="0" aria-valuemax="100" style="width: 85.71%">
           <span class="sr-only">85.71% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">85.71%</div></td>
       <td class="success small"><div align="right">12&nbsp;/&nbsp;14</div></td>
       <td class="success small">27.94</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#25"><abbr title="addHeader(string $name, string $value): void">addHeader</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#36"><abbr title="setHeaders(array $headers): void">setHeaders</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#47"><abbr title="getHeader(string $name): ?string">getHeader</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#57"><abbr title="getAll(): array">getAll</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#67"><abbr title="clear(): void">clear</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#78"><abbr title="hasHeader(string $name): bool">hasHeader</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#89"><abbr title="removeHeader(string $name): void">removeHeader</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#101"><abbr title="setFileContentHeaders(string $mimeType, string $filename): void">setFileContentHeaders</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#117"><abbr title="sanitizeFilename(string $filename): string">sanitizeFilename</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">4&nbsp;/&nbsp;4</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#138"><abbr title="prepareCacheHeaders(int $cacheExpires, ?string $etag, ?int $lastModified): void">prepareCacheHeaders</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">11&nbsp;/&nbsp;11</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">4</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#168"><abbr title="sendPreparedHeaders(): void">sendPreparedHeaders</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="40.00" aria-valuemin="0" aria-valuemax="100" style="width: 40.00%">
           <span class="sr-only">40.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">40.00%</div></td>
       <td class="warning small"><div align="right">2&nbsp;/&nbsp;5</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">4.94</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#189"><abbr title="flushHeaders(): void">flushHeaders</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#204"><abbr title="send304NotModified(int $cacheExpires, ?string $etag, ?int $lastModified): void">send304NotModified</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#223"><abbr title="handleCacheValidation(int $cacheExpires, ?string $etag, ?int $lastModified): bool">handleCacheValidation</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">11&nbsp;/&nbsp;11</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">7</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="default">\</span><span class="default">Streamer</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;StreamHeaders&nbsp;class&nbsp;for&nbsp;managing&nbsp;HTTP&nbsp;headers</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;This&nbsp;class&nbsp;encapsulates&nbsp;HTTP&nbsp;header&nbsp;storage&nbsp;and&nbsp;manipulation&nbsp;functionality,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;providing&nbsp;a&nbsp;clean&nbsp;interface&nbsp;for&nbsp;header&nbsp;management&nbsp;in&nbsp;streaming&nbsp;operations.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">StreamHeaders</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;array&nbsp;All&nbsp;headers&nbsp;stored&nbsp;as&nbsp;name&nbsp;=&gt;&nbsp;value&nbsp;pairs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$headers</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Add&nbsp;a&nbsp;single&nbsp;header</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$name&nbsp;Header&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$value&nbsp;Header&nbsp;value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$name</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="43 tests cover line 27" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWithoutOutputBuffering&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHeadersWithEmptyValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWithOutputBuffering&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testAddHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithUnicodeFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithVeryLongFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHeaderCaseSensitivity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWhenHeadersAlreadySent&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testAddMultipleHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testAddHeaderOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testGetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testGetAll&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHasHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testRemoveHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testClear&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithInvalidCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetHeadersWithEmptyArray&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">headers</span><span class="keyword">[</span><span class="default">$name</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;multiple&nbsp;headers&nbsp;at&nbsp;once&nbsp;(replacing&nbsp;existing&nbsp;headers)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;array&nbsp;$headers&nbsp;Array&nbsp;of&nbsp;header&nbsp;name&nbsp;=&gt;&nbsp;value&nbsp;pairs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setHeaders</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$headers</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 38" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetHeadersWithEmptyArray&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">headers</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$headers</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;a&nbsp;specific&nbsp;header&nbsp;value&nbsp;by&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$name&nbsp;Header&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string|null&nbsp;Header&nbsp;value&nbsp;or&nbsp;null&nbsp;if&nbsp;not&nbsp;found</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getHeader</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$name</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="25 tests cover line 49" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHeadersWithEmptyValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testAddHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithUnicodeFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithVeryLongFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHeaderCaseSensitivity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testAddMultipleHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testAddHeaderOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testGetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithInvalidCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">headers</span><span class="keyword">[</span><span class="default">$name</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Return&nbsp;all&nbsp;headers&nbsp;as&nbsp;an&nbsp;associative&nbsp;array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&nbsp;Array&nbsp;of&nbsp;header&nbsp;name&nbsp;=&gt;&nbsp;value&nbsp;pairs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getAll</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 59" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWithNoHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHeaderCaseSensitivity&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testGetAll&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testGetAllEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testRemoveNonExistentHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testClear&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetHeadersWithEmptyArray&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">headers</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Clear&nbsp;all&nbsp;headers</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">clear</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 69" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testClear&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">headers</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Check&nbsp;if&nbsp;a&nbsp;header&nbsp;exists</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$name&nbsp;Header&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;bool&nbsp;True&nbsp;if&nbsp;header&nbsp;exists,&nbsp;false&nbsp;otherwise</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">hasHeader</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$name</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 80" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHeadersWithEmptyValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testAddHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHasHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testRemoveHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testClear&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetHeadersWithEmptyArray&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">headers</span><span class="keyword">[</span><span class="default">$name</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Remove&nbsp;a&nbsp;specific&nbsp;header</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$name&nbsp;Header&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">removeHeader</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$name</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 91" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testRemoveHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testRemoveNonExistentHeader&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">unset</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">headers</span><span class="keyword">[</span><span class="default">$name</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;file&nbsp;content&nbsp;headers&nbsp;(Content-Type&nbsp;and&nbsp;Content-Disposition)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$mimeType&nbsp;The&nbsp;MIME&nbsp;type&nbsp;for&nbsp;the&nbsp;content</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$filename&nbsp;The&nbsp;filename&nbsp;to&nbsp;present&nbsp;to&nbsp;the&nbsp;browser</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setFileContentHeaders</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$mimeType</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Content&nbsp;type</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 104" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithUnicodeFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithVeryLongFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithInvalidCharacters&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">'Content-Type'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$mimeType</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Content&nbsp;disposition</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 107" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithUnicodeFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithVeryLongFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithInvalidCharacters&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$safeFilename</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">sanitizeFilename</span><span class="keyword">(</span><span class="default">$filename</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 108" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithUnicodeFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithVeryLongFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithInvalidCharacters&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">'Content-Disposition'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'attachment;&nbsp;filename=&quot;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$safeFilename</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&quot;'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Sanitize&nbsp;filename&nbsp;for&nbsp;safe&nbsp;HTTP&nbsp;header&nbsp;usage</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$filename&nbsp;Original&nbsp;filename</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;Sanitized&nbsp;filename</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">sanitizeFilename</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Remove&nbsp;or&nbsp;replace&nbsp;problematic&nbsp;characters</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 120" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithUnicodeFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithVeryLongFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithInvalidCharacters&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$filename</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">preg_replace</span><span class="keyword">(</span><span class="default">'/[^\w\-_\.]/'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'_'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Ensure&nbsp;it's&nbsp;not&nbsp;empty</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 123" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithUnicodeFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithVeryLongFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithInvalidCharacters&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$filename</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 124" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$filename</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">'download'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 127" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSanitizeFilenameThroughSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithUnicodeFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithOnlyExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testFileContentHeadersWithVeryLongFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithEmptyFilename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSetFileContentHeadersWithInvalidCharacters&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Prepare&nbsp;cache-related&nbsp;headers</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int&nbsp;$cacheExpires&nbsp;Cache&nbsp;expiration&nbsp;time&nbsp;in&nbsp;seconds&nbsp;(0&nbsp;=&nbsp;no&nbsp;cache)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string|null&nbsp;$etag&nbsp;ETag&nbsp;for&nbsp;cache&nbsp;validation</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int|null&nbsp;$lastModified&nbsp;Last&nbsp;modified&nbsp;timestamp</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">prepareCacheHeaders</span><span class="keyword">(</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$cacheExpires</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$etag</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$lastModified</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="16 tests cover line 140" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$cacheExpires</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">0</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;No&nbsp;cache&nbsp;headers</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 142" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersNoCache&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">'Cache-Control'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'no-cache,&nbsp;no-store,&nbsp;must-revalidate'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 143" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersNoCache&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">'Pragma'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'no-cache'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 144" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersNoCache&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">'Expires'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'0'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Cache&nbsp;headers</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 147" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$expiresTime</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">time</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">+</span><span class="default">&nbsp;</span><span class="default">$cacheExpires</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 148" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">'Cache-Control'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'public,&nbsp;max-age='</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$cacheExpires</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 149" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">'Expires'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">gmdate</span><span class="keyword">(</span><span class="default">'D,&nbsp;d&nbsp;M&nbsp;Y&nbsp;H:i:s'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$expiresTime</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&nbsp;GMT'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;ETag&nbsp;header</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="16 tests cover line 153" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$etag</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 154" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">'ETag'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'&quot;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$etag</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&quot;'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Last-Modified&nbsp;header</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="16 tests cover line 158" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithNegativeExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$lastModified</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 159" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheHeadersWithZeroExpiration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testMultipleCallsToPrepareCacheHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testPrepareCacheHeadersWithAllOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">addHeader</span><span class="keyword">(</span><span class="default">'Last-Modified'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">gmdate</span><span class="keyword">(</span><span class="default">'D,&nbsp;d&nbsp;M&nbsp;Y&nbsp;H:i:s'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$lastModified</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&nbsp;GMT'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Send&nbsp;all&nbsp;prepared&nbsp;headers&nbsp;to&nbsp;the&nbsp;browser</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">sendPreparedHeaders</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Check&nbsp;if&nbsp;headers&nbsp;have&nbsp;already&nbsp;been&nbsp;sent</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="15 tests cover line 171" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWithoutOutputBuffering&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWithNoHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWithOutputBuffering&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWhenHeadersAlreadySent&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">headers_sent</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="15 tests cover line 172" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWithoutOutputBuffering&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWithNoHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWithOutputBuffering&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSendPreparedHeadersWhenHeadersAlreadySent&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Send&nbsp;all&nbsp;prepared&nbsp;headers</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">headers</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$name</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">header</span><span class="keyword">(</span><span class="default">$name</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">':&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Flush&nbsp;headers</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">flushHeaders</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Flush&nbsp;headers&nbsp;to&nbsp;the&nbsp;browser</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">flushHeaders</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">ob_get_level</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">0</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">flush</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Send&nbsp;304&nbsp;Not&nbsp;Modified&nbsp;response&nbsp;with&nbsp;cache&nbsp;headers</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int&nbsp;$cacheExpires&nbsp;Cache&nbsp;expiration&nbsp;time&nbsp;in&nbsp;seconds&nbsp;(0&nbsp;=&nbsp;no&nbsp;cache)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string|null&nbsp;$etag&nbsp;ETag&nbsp;for&nbsp;cache&nbsp;validation</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int|null&nbsp;$lastModified&nbsp;Last&nbsp;modified&nbsp;timestamp</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">send304NotModified</span><span class="keyword">(</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$cacheExpires</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$etag</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$lastModified</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 206" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">http_response_code</span><span class="keyword">(</span><span class="default">304</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Prepare&nbsp;cache&nbsp;headers&nbsp;for&nbsp;304&nbsp;response</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 209" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">prepareCacheHeaders</span><span class="keyword">(</span><span class="default">$cacheExpires</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$etag</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$lastModified</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Send&nbsp;the&nbsp;prepared&nbsp;headers</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 212" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testSend304NotModifiedWithNoCache&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">sendPreparedHeaders</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Handle&nbsp;cache&nbsp;validation&nbsp;and&nbsp;send&nbsp;304&nbsp;response&nbsp;if&nbsp;appropriate</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int&nbsp;$cacheExpires&nbsp;Cache&nbsp;expiration&nbsp;time&nbsp;in&nbsp;seconds&nbsp;(0&nbsp;=&nbsp;no&nbsp;cache)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string|null&nbsp;$etag&nbsp;ETag&nbsp;for&nbsp;cache&nbsp;validation</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int|null&nbsp;$lastModified&nbsp;Last&nbsp;modified&nbsp;timestamp</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;bool&nbsp;True&nbsp;if&nbsp;304&nbsp;Not&nbsp;Modified&nbsp;was&nbsp;sent,&nbsp;false&nbsp;otherwise</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handleCacheValidation</span><span class="keyword">(</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$cacheExpires</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$etag</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$lastModified</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Check&nbsp;If-None-Match&nbsp;(ETag&nbsp;validation)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="16 tests cover line 226" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithInvalidIfModifiedSince&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testComplexCacheValidationScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithStrtoimeFailure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithNullParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithNoClientHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMismatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedNewer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$etag</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$_SERVER</span><span class="keyword">[</span><span class="default">'HTTP_IF_NONE_MATCH'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 227" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testComplexCacheValidationScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMismatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$clientEtag</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">trim</span><span class="keyword">(</span><span class="default">$_SERVER</span><span class="keyword">[</span><span class="default">'HTTP_IF_NONE_MATCH'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'&quot;'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 228" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testComplexCacheValidationScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMismatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$clientEtag</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">$etag</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 229" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">send304NotModified</span><span class="keyword">(</span><span class="default">$cacheExpires</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$etag</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$lastModified</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 230" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagButNoLastModified&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithBothETagAndLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Check&nbsp;If-Modified-Since&nbsp;(Last-Modified&nbsp;validation)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 235" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithInvalidIfModifiedSince&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testComplexCacheValidationScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithStrtoimeFailure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithNullParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithNoClientHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMismatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedNewer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$lastModified</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$_SERVER</span><span class="keyword">[</span><span class="default">'HTTP_IF_MODIFIED_SINCE'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 236" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithInvalidIfModifiedSince&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testComplexCacheValidationScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithStrtoimeFailure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedNewer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$clientLastModified</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">strtotime</span><span class="keyword">(</span><span class="default">$_SERVER</span><span class="keyword">[</span><span class="default">'HTTP_IF_MODIFIED_SINCE'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 237" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithInvalidIfModifiedSince&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testComplexCacheValidationScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithStrtoimeFailure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedNewer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$clientLastModified</span><span class="default">&nbsp;</span><span class="default">&gt;=</span><span class="default">&nbsp;</span><span class="default">$lastModified</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 238" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">send304NotModified</span><span class="keyword">(</span><span class="default">$cacheExpires</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$etag</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$lastModified</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 239" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedButNoETag&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithETagMismatchButLastModifiedMatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedMatch&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="240" href="#240">240</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="241" href="#241">241</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="242" href="#242">242</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 243" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithInvalidIfModifiedSince&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testComplexCacheValidationScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testCacheValidationWithStrtoimeFailure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithNullParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithNoClientHeaders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithETagMismatch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Streamer\StreamHeadersTest::testHandleCacheValidationWithLastModifiedNewer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="243" href="#243">243</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">false</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="244" href="#244">244</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="245" href="#245">245</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Mon Jun 23 17:03:03 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/popper.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/bootstrap.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/file.js?v=9.2.32" type="text/javascript"></script>
 </body>
</html>
