<?php

namespace Tests\Nzoom\Export\Provider;

use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Provider\ModelTableProvider;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Entity\ExportTable;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Tests\Nzoom\Export\ExportTestCase;

class ModelTableProviderTest extends ExportTestCase
{
    private ModelTableProvider $provider;
    private RegistryMock $mockRegistry;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../../TestHelpers/GlobalMocks.php';

        // Include General class for date formatting
        if (!class_exists('General')) {
            require_once __DIR__ . '/../../../../inc/common/general.class.php';
        }

        $this->mockRegistry = new RegistryMock();
        $this->provider = new ModelTableProvider($this->mockRegistry);
    }

    public function testConstructorWithDefaultOptions(): void
    {
        $provider = new ModelTableProvider($this->mockRegistry);
        $this->assertInstanceOf(ModelTableProvider::class, $provider);
    }

    public function testConstructorWithCustomOptions(): void
    {
        $options = [
            'include_empty_tables' => true,
            'date_format' => 'd/m/Y',
            'datetime_format' => 'd/m/Y H:i:s'
        ];

        $provider = new ModelTableProvider($this->mockRegistry, $options);
        $this->assertInstanceOf(ModelTableProvider::class, $provider);
    }

    public function testGetTablesForRecordWithNonModelRecord(): void
    {
        $record = new \stdClass();
        $collection = $this->provider->getTablesForRecord($record);

        $this->assertInstanceOf(ExportTableCollection::class, $collection);
        $this->assertFalse($collection->hasTables());
    }

    public function testGetTablesForRecordWithModelWithoutVariables(): void
    {
        // Create model with no variables
        $model = new \Model(['id' => 123], [], [], []);

        $collection = $this->provider->getTablesForRecord($model);

        $this->assertInstanceOf(ExportTableCollection::class, $collection);
        $this->assertFalse($collection->hasTables());
    }

    public function testGetTablesForRecordWithModelWithGroupingVariables(): void
    {
        $model = $this->createMockModelWithGroupingData();

        $collection = $this->provider->getTablesForRecord($model);

        $this->assertInstanceOf(ExportTableCollection::class, $collection);
        $this->assertTrue($collection->hasTables());
        $this->assertEquals(1, $collection->count());

        $tables = $collection->getTables();
        $table = array_values($tables)[0];
        $this->assertInstanceOf(ExportTable::class, $table);
        $this->assertEquals('purchases', $table->getTableType());
        $this->assertEquals('Purchases', $table->getTableName());
    }

    public function testGetTablesForRecordWithIncludeEmptyTables(): void
    {
        $model = $this->createMockModelWithEmptyGroupingData();

        // Test without include_empty_tables (default false)
        $collection = $this->provider->getTablesForRecord($model);
        $this->assertFalse($collection->hasTables());

        // Test with include_empty_tables = true
        $collection = $this->provider->getTablesForRecord($model, ['include_empty_tables' => true]);
        $this->assertTrue($collection->hasTables());
    }

    public function testGetTablesForRecordWithException(): void
    {
        // Create a model that will throw an exception when checkForVariables is called
        $model = new class extends \Model {
            public function checkForVariables() {
                throw new \Exception('Test exception');
            }
        };

        // Mock logger to capture warning
        $mockLogger = new class {
            public $warningCalled = false;
            public $warningMessage = '';

            public function warning($message) {
                $this->warningCalled = true;
                $this->warningMessage = $message;
            }
        };
        $this->mockRegistry['logger'] = $mockLogger;

        $collection = $this->provider->getTablesForRecord($model);

        $this->assertInstanceOf(ExportTableCollection::class, $collection);
        $this->assertFalse($collection->hasTables());
        $this->assertTrue($mockLogger->warningCalled);
        $this->assertStringContainsString('Failed to discover grouping variables from model', $mockLogger->warningMessage);
    }

    public function testCreateTableFromGroupingDataWithEmptyNamesOrLabels(): void
    {
        $model = $this->createMockModel();

        // Test with empty names
        $groupingData = [
            'names' => [],
            'labels' => ['Item', 'Price'],
            'hidden' => [],
            'values' => []
        ];

        $result = $this->invokePrivateMethod($this->provider, 'createTableFromGroupingData', [
            $model, 'test', $groupingData, []
        ]);

        $this->assertNull($result);

        // Test with empty labels
        $groupingData = [
            'names' => ['item', 'price'],
            'labels' => [],
            'hidden' => [],
            'values' => []
        ];

        $result = $this->invokePrivateMethod($this->provider, 'createTableFromGroupingData', [
            $model, 'test', $groupingData, []
        ]);

        $this->assertNull($result);
    }

    public function testGetOrCreateTableHeaderCaching(): void
    {
        $names = ['item', 'price'];
        $labels = ['Item', 'Price'];
        $hidden = [];

        // First call should create header
        $header1 = $this->invokePrivateMethod($this->provider, 'getOrCreateTableHeader', [
            'test_table', $names, $labels, $hidden
        ]);

        // Second call should return cached header
        $header2 = $this->invokePrivateMethod($this->provider, 'getOrCreateTableHeader', [
            'test_table', $names, $labels, $hidden
        ]);

        $this->assertSame($header1, $header2);
    }

    public function testGetOrCreateTableHeaderWithHiddenColumns(): void
    {
        // NOTE: This test documents the buggy behavior where all columns get hidden
        // due to loose comparison in in_array()

        $names = ['item', 'price', 'secret'];
        $labels = ['Item', 'Price', 'Secret'];
        $hidden = ['secret', 1]; // Hide by name and by index

        $header = $this->invokePrivateMethod($this->provider, 'getOrCreateTableHeader', [
            'test_table', $names, $labels, $hidden
        ]);

        $this->assertInstanceOf(ExportHeader::class, $header);
        // Due to the bug, all columns are hidden
        $this->assertEquals(0, $header->count());
    }

    public function testFormatTableName(): void
    {
        $testCases = [
            'snake_case' => 'Snake Case',
            'camelCase' => 'CamelCase',
            'kebab-case' => 'Kebab Case',
            'simple' => 'Simple',
            'multiple_words_here' => 'Multiple Words Here'
        ];

        foreach ($testCases as $input => $expected) {
            $result = $this->invokePrivateMethod($this->provider, 'formatTableName', [$input]);
            $this->assertEquals($expected, $result, "Failed for input: $input");
        }
    }

    public function testGuessColumnType(): void
    {
        $testCases = [
            // Date patterns
            'date' => ExportValue::TYPE_DATE,
            'created_date' => ExportValue::TYPE_DATE,
            'modified_date' => ExportValue::TYPE_DATE,

            // DateTime patterns
            'datetime' => ExportValue::TYPE_DATETIME,
            'timestamp' => ExportValue::TYPE_DATETIME,
            'created_at' => ExportValue::TYPE_DATETIME,
            'updated_at' => ExportValue::TYPE_DATETIME,

            // Integer patterns
            'id' => ExportValue::TYPE_INTEGER,
            'count' => ExportValue::TYPE_INTEGER,
            'quantity' => ExportValue::TYPE_INTEGER,
            'number' => ExportValue::TYPE_INTEGER,

            // Float patterns (need to match numeric pattern first)
            'price' => ExportValue::TYPE_FLOAT,
            'amount' => ExportValue::TYPE_FLOAT,
            'total' => ExportValue::TYPE_FLOAT,
            'sum' => ExportValue::TYPE_FLOAT,
            'price_rate' => ExportValue::TYPE_FLOAT, // Contains both 'price' and 'rate'
            'total_cost' => ExportValue::TYPE_FLOAT, // Contains both 'total' and 'cost'

            // Boolean patterns
            'is_active' => ExportValue::TYPE_BOOLEAN,
            'has_permission' => ExportValue::TYPE_BOOLEAN,
            'can_edit' => ExportValue::TYPE_BOOLEAN,
            'active' => ExportValue::TYPE_BOOLEAN,
            'enabled' => ExportValue::TYPE_BOOLEAN,
            'disabled' => ExportValue::TYPE_BOOLEAN,
            'visible' => ExportValue::TYPE_BOOLEAN,
            'is_active' => ExportValue::TYPE_BOOLEAN, // Boolean pattern without 'id' substring

            // String (default)
            'name' => ExportValue::TYPE_STRING,
            'description' => ExportValue::TYPE_STRING,
            'random_field' => ExportValue::TYPE_STRING
        ];

        foreach ($testCases as $input => $expected) {
            $result = $this->invokePrivateMethod($this->provider, 'guessColumnType', [$input]);
            $this->assertEquals($expected, $result, "Failed for input: $input");
        }
    }

    public function testFormatValue(): void
    {
        // Test null value
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            null, ExportValue::TYPE_STRING, null, []
        ]);
        $this->assertNull($result);

        // Test date conversion
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            '2023-12-25', ExportValue::TYPE_DATE, null, []
        ]);
        $this->assertInstanceOf(\DateTime::class, $result);

        // Test datetime conversion
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            '2023-12-25 15:30:00', ExportValue::TYPE_DATETIME, null, []
        ]);
        $this->assertInstanceOf(\DateTime::class, $result);

        // Test integer conversion
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            '123', ExportValue::TYPE_INTEGER, null, []
        ]);
        $this->assertEquals(123, $result);
        $this->assertIsInt($result);

        // Test float conversion
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            '123.45', ExportValue::TYPE_FLOAT, null, []
        ]);
        $this->assertEquals(123.45, $result);
        $this->assertIsFloat($result);

        // Test boolean conversion
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            '1', ExportValue::TYPE_BOOLEAN, null, []
        ]);
        $this->assertTrue($result);
        $this->assertIsBool($result);

        // Test string (no conversion)
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            'test string', ExportValue::TYPE_STRING, null, []
        ]);
        $this->assertEquals('test string', $result);
    }

    public function testGetTableConfiguration(): void
    {
        $config = $this->provider->getTableConfiguration('test_table');

        $this->assertIsArray($config);
        $this->assertEquals('Test Table', $config['name']);
        $this->assertTrue($config['auto_discovered']);
    }

    public function testValidateRecord(): void
    {
        $model = $this->createMockModel();
        $this->assertTrue($this->provider->validateRecord($model));
        $this->assertTrue($this->provider->validateRecord($model, ['purchases', 'orders']));

        $nonModel = new \stdClass();
        $this->assertFalse($this->provider->validateRecord($nonModel));
    }

    private function createMockModel(): \Model
    {
        $model = new \Model(['id' => 123], [], [], []);
        return $model;
    }

    private function createMockModelWithGroupingData(): \Model
    {
        $varsForTemplate = [
            'purchases' => [
                'type' => 'grouping',
                'names' => ['item', 'price'],
                'labels' => ['Item', 'Price'],
                'hidden' => [],
                'values' => [
                    ['Laptop', 999.99],
                    ['Mouse', 29.99]
                ]
            ]
        ];

        $model = new \Model(['id' => 123], [], [], $varsForTemplate);
        return $model;
    }

    private function createMockModelWithEmptyGroupingData(): \Model
    {
        $varsForTemplate = [
            'empty_table' => [
                'type' => 'grouping',
                'names' => ['item', 'price'],
                'labels' => ['Item', 'Price'],
                'hidden' => [],
                'values' => [] // Empty values
            ]
        ];

        $model = new \Model(['id' => 123], [], [], $varsForTemplate);
        return $model;
    }

    public function testDiscoverGroupingVariablesWithSanitizedModel(): void
    {
        $varsForTemplate = [
            'purchases' => [
                'type' => 'grouping',
                'names' => ['item'],
                'labels' => ['Item'],
                'hidden' => [],
                'values' => []
            ]
        ];

        $model = new \Model(['id' => 123], [], [], $varsForTemplate);
        $model->sanitize(); // Make it sanitized

        $result = $this->invokePrivateMethod($this->provider, 'discoverGroupingVariables', [$model]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('purchases', $result);
        $this->assertTrue($model->isSanitized()); // Should be re-sanitized after the call
    }

    public function testDiscoverGroupingVariablesWithException(): void
    {
        // Create a model that will throw an exception when checkForVariables is called
        $model = new class extends \Model {
            public function checkForVariables() {
                throw new \Exception('Test exception');
            }
        };

        // Mock logger to capture warning
        $mockLogger = new class {
            public $warningCalled = false;
            public $warningMessage = '';

            public function warning($message) {
                $this->warningCalled = true;
                $this->warningMessage = $message;
            }
        };
        $this->mockRegistry['logger'] = $mockLogger;

        $result = $this->invokePrivateMethod($this->provider, 'discoverGroupingVariables', [$model]);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
        $this->assertTrue($mockLogger->warningCalled);
        $this->assertStringContainsString('Failed to discover grouping variables from model', $mockLogger->warningMessage);
    }

    public function testDiscoverGroupingVariablesWithNonGroupingTypes(): void
    {
        $varsForTemplate = [
            'regular_var' => [
                'type' => 'string',
                'value' => 'test'
            ],
            'another_var' => [
                'type' => 'number',
                'value' => 123
            ],
            'purchases' => [
                'type' => 'grouping',
                'names' => ['item'],
                'labels' => ['Item'],
                'hidden' => [],
                'values' => []
            ]
        ];

        $model = new \Model(['id' => 123], [], [], $varsForTemplate);

        $result = $this->invokePrivateMethod($this->provider, 'discoverGroupingVariables', [$model]);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertArrayHasKey('purchases', $result);
        $this->assertArrayNotHasKey('regular_var', $result);
        $this->assertArrayNotHasKey('another_var', $result);
    }

    public function testFormatValueWithDateTimeObjects(): void
    {
        $dateTime = new \DateTime('2023-12-25 15:30:00');

        // Test with DateTime object for date type
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            $dateTime, ExportValue::TYPE_DATE, null, []
        ]);
        $this->assertSame($dateTime, $result);

        // Test with DateTime object for datetime type
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            $dateTime, ExportValue::TYPE_DATETIME, null, []
        ]);
        $this->assertSame($dateTime, $result);
    }

    public function testFormatValueWithInvalidDateStrings(): void
    {
        // Test with invalid date string for date type
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            'invalid-date', ExportValue::TYPE_DATE, null, []
        ]);
        $this->assertEquals('invalid-date', $result);

        // Test with invalid date string for datetime type
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            'invalid-datetime', ExportValue::TYPE_DATETIME, null, []
        ]);
        $this->assertEquals('invalid-datetime', $result);
    }

    public function testCreateRecordFromRowDataWithMissingValues(): void
    {
        $rowData = ['Laptop']; // Missing second value
        $names = ['item', 'price'];
        $hidden = [];
        $options = [];

        $record = $this->invokePrivateMethod($this->provider, 'createRecordFromRowData', [
            $rowData, $names, $hidden, [], $options
        ]);

        $this->assertInstanceOf(ExportRecord::class, $record);
        $this->assertEquals(2, $record->count());

        // First value should be 'Laptop'
        $itemValue = $record->getValueByColumnName('item');
        $this->assertEquals('Laptop', $itemValue->getValue());

        // Second value should be null
        $priceValue = $record->getValueByColumnName('price');
        $this->assertNull($priceValue->getValue());
    }

    public function testCreateRecordFromRowDataWithHiddenColumns(): void
    {
        // NOTE: This test documents the buggy behavior where all columns get hidden

        $rowData = ['Laptop', 999.99, 'secret'];
        $names = ['item', 'price', 'secret_field'];
        $hidden = ['secret_field', 1]; // Hide by name and by index
        $options = [];

        $record = $this->invokePrivateMethod($this->provider, 'createRecordFromRowData', [
            $rowData, $names, $hidden, [], $options
        ]);

        $this->assertInstanceOf(ExportRecord::class, $record);
        // Due to the bug, all columns are hidden
        $this->assertEquals(0, $record->count());

        // All values should be null due to the bug
        $this->assertNull($record->getValueByColumnName('item'));
        $this->assertNull($record->getValueByColumnName('price'));
        $this->assertNull($record->getValueByColumnName('secret_field'));
    }

    public function testPopulateTableFromGroupingData(): void
    {
        $table = new ExportTable(
            'test',
            'Test Table',
            new ExportHeader(),
            123,
            []
        );

        $values = [
            ['Laptop', 999.99],
            ['Mouse', 29.99]
        ];
        $names = ['item', 'price'];
        $hidden = [];
        $options = [];

        $this->invokePrivateMethod($this->provider, 'populateTableFromGroupingData', [
            $table, $values, $names, $hidden, [], $options
        ]);

        $this->assertEquals(2, $table->count());
        $this->assertTrue($table->hasRecords());
    }

    public function testComplexGroupingDataScenario(): void
    {
        $varsForTemplate = [
            'orders' => [
                'type' => 'grouping',
                'names' => ['order_id', 'product_name', 'quantity', 'unit_price', 'is_shipped', 'order_date'],
                'labels' => ['Order ID', 'Product', 'Qty', 'Price', 'Shipped', 'Date'],
                'hidden' => [],
                'values' => [
                    [1001, 'Laptop Pro', 2, 1299.99, true, '2023-12-01'],
                    [1002, 'Wireless Mouse', 5, 49.99, false, '2023-12-02']
                ]
            ]
        ];

        $model = new \Model(['id' => 123], [], [], $varsForTemplate);

        $collection = $this->provider->getTablesForRecord($model);

        $this->assertTrue($collection->hasTables());
        $this->assertEquals(1, $collection->count());

        $tables = $collection->getTables();
        $table = array_values($tables)[0];
        $this->assertEquals('orders', $table->getTableType());
        $this->assertEquals('Orders', $table->getTableName());
        $this->assertEquals(2, $table->count());
    }

    public function testHiddenColumnLogicWithBuggyBehavior(): void
    {
        // NOTE: This test documents a BUG in ModelTableProvider
        // The issue is that in_array() uses loose comparison, so in_array(0, ['secret_field', 1]) returns true
        // because PHP considers 0 == 'secret_field' in loose comparison

        $names = ['item', 'price', 'secret_field'];
        $hidden = ['secret_field', 1]; // Hide by name and by index

        $expectedVisible = [];
        foreach ($names as $index => $varName) {
            // This mimics the buggy behavior in ModelTableProvider
            $hiddenByName = in_array($varName, $hidden); // Uses loose comparison
            $hiddenByIndex = in_array($index, $hidden);  // Uses loose comparison

            if (!$hiddenByName && !$hiddenByIndex) {
                $expectedVisible[] = $varName;
            }
        }

        // Due to the bug, all columns are hidden because:
        // - index 0 ('item'): in_array(0, ['secret_field', 1]) returns true (0 == 'secret_field' in loose comparison)
        // - index 1 ('price'): in_array(1, ['secret_field', 1]) returns true (exact match)
        // - index 2 ('secret_field'): in_array('secret_field', ['secret_field', 1]) returns true (exact match)
        $this->assertEquals([], $expectedVisible);
    }

    public function testHiddenColumnsWorkingCorrectlyWithOnlyNumericIndices(): void
    {
        // When using only numeric indices, the feature works correctly
        $names = ['item', 'price', 'secret'];
        $hidden = [1, 2]; // Hide by index only

        $header = $this->invokePrivateMethod($this->provider, 'getOrCreateTableHeader', [
            'test_table', $names, $names, $hidden
        ]);

        $this->assertInstanceOf(ExportHeader::class, $header);
        $this->assertEquals(1, $header->count()); // Only 'item' should remain

        $columns = $header->getColumns();
        $this->assertCount(1, $columns);
        $this->assertEquals('item', $columns[0]->getVarName());
    }

    public function testHiddenColumnsStillBuggyWithStringNames(): void
    {
        // Even with only string names, the bug still occurs because:
        // in_array(0, ['price', 'secret']) returns true (0 == 'price' in loose comparison)

        $names = ['item', 'price', 'secret'];
        $hidden = ['price', 'secret']; // Hide by name only

        // Test the PHP behavior that causes the bug
        $this->assertTrue(in_array(0, ['price', 'secret']), 'PHP loose comparison bug: 0 == "price"');

        $header = $this->invokePrivateMethod($this->provider, 'getOrCreateTableHeader', [
            'test_table', $names, $names, $hidden
        ]);

        $this->assertInstanceOf(ExportHeader::class, $header);
        // Due to the bug, all columns are still hidden
        $this->assertEquals(0, $header->count());
    }

    public function testFormatValueWithAllTypes(): void
    {
        // Test all branches of formatValue method

        // Test invalid date strings (should return original value)
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            'not-a-date', ExportValue::TYPE_DATE, null, []
        ]);
        $this->assertEquals('not-a-date', $result);

        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            'not-a-datetime', ExportValue::TYPE_DATETIME, null, []
        ]);
        $this->assertEquals('not-a-datetime', $result);

        // Test boolean conversions
        $testCases = [
            ['1', true],
            ['0', false],
            [1, true],
            [0, false],
            ['true', true],
            ['false', true], // Non-empty string is truthy
            ['', false],
        ];

        foreach ($testCases as [$input, $expected]) {
            $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
                $input, ExportValue::TYPE_BOOLEAN, null, []
            ]);
            $this->assertEquals($expected, $result, "Failed for boolean input: " . var_export($input, true));
        }

        // Test string type (should return unchanged)
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            'test string', ExportValue::TYPE_STRING, null, []
        ]);
        $this->assertEquals('test string', $result);

        // Test unknown type (should return unchanged)
        $result = $this->invokePrivateMethod($this->provider, 'formatValue', [
            'test value', 'unknown_type', null, []
        ]);
        $this->assertEquals('test value', $result);
    }

    public function testGuessColumnTypeAllPatterns(): void
    {
        // Test all regex patterns in guessColumnType

        // Date patterns that should return DATE (not DATETIME)
        $dateOnlyPatterns = [
            'birth_date',
            'start_date',
            'end_date',
            'modified_date',
        ];

        foreach ($dateOnlyPatterns as $pattern) {
            $result = $this->invokePrivateMethod($this->provider, 'guessColumnType', [$pattern]);
            $this->assertEquals(ExportValue::TYPE_DATE, $result, "Failed for date pattern: $pattern");
        }

        // Integer patterns (numeric but not float)
        $integerPatterns = [
            'user_id',
            'item_count',
            'product_quantity',
            'record_number',
        ];

        foreach ($integerPatterns as $pattern) {
            $result = $this->invokePrivateMethod($this->provider, 'guessColumnType', [$pattern]);
            $this->assertEquals(ExportValue::TYPE_INTEGER, $result, "Failed for integer pattern: $pattern");
        }

        // Boolean patterns
        $booleanPatterns = [
            'has_permission',
            'can_edit',
            'active',
            'enabled',
            'disabled',
            'visible',
        ];

        foreach ($booleanPatterns as $pattern) {
            $result = $this->invokePrivateMethod($this->provider, 'guessColumnType', [$pattern]);
            $this->assertEquals(ExportValue::TYPE_BOOLEAN, $result, "Failed for boolean pattern: $pattern");
        }
    }

    public function testShouldSkipTableWithMultipleRecords(): void
    {
        // Create a table with multiple records - should not be skipped
        $table = new ExportTable('test', 'Test Table', new ExportHeader(), 123, []);

        $record1 = new ExportRecord();
        $record1->addValue('item', '', ExportValue::TYPE_STRING);
        $record1->addValue('price', 0, ExportValue::TYPE_INTEGER);

        $record2 = new ExportRecord();
        $record2->addValue('item', '', ExportValue::TYPE_STRING);
        $record2->addValue('price', 0, ExportValue::TYPE_INTEGER);

        $table->addRecord($record1, false);
        $table->addRecord($record2, false);

        $result = $this->invokePrivateMethod($this->provider, 'shouldSkipTable', [$table]);
        $this->assertFalse($result, 'Tables with multiple records should not be skipped');
    }

    public function testShouldSkipTableWithSingleEmptyRecord(): void
    {
        // Create a table with one record containing only empty/zero values - should be skipped
        $table = new ExportTable('test', 'Test Table', new ExportHeader(), 123, []);

        $record = new ExportRecord();
        $record->addValue('item', '', ExportValue::TYPE_STRING);
        $record->addValue('price', 0, ExportValue::TYPE_INTEGER);
        $record->addValue('active', false, ExportValue::TYPE_BOOLEAN);
        $record->addValue('description', null, ExportValue::TYPE_STRING);

        $table->addRecord($record, false);

        $result = $this->invokePrivateMethod($this->provider, 'shouldSkipTable', [$table]);
        $this->assertTrue($result, 'Tables with single empty record should be skipped');
    }

    public function testShouldNotSkipTableWithSingleNonEmptyRecord(): void
    {
        // Create a table with one record containing at least one non-empty value - should not be skipped
        $table = new ExportTable('test', 'Test Table', new ExportHeader(), 123, []);

        $record = new ExportRecord();
        $record->addValue('item', 'Laptop', ExportValue::TYPE_STRING);
        $record->addValue('price', 0, ExportValue::TYPE_INTEGER);
        $record->addValue('active', false, ExportValue::TYPE_BOOLEAN);

        $table->addRecord($record, false);

        $result = $this->invokePrivateMethod($this->provider, 'shouldSkipTable', [$table]);
        $this->assertFalse($result, 'Tables with single non-empty record should not be skipped');
    }

    public function testIsTableRowEmptyWithAllEmptyValues(): void
    {
        $record = new ExportRecord();
        $record->addValue('item', '', ExportValue::TYPE_STRING);
        $record->addValue('price', 0, ExportValue::TYPE_INTEGER);
        $record->addValue('amount', 0.0, ExportValue::TYPE_FLOAT);
        $record->addValue('active', false, ExportValue::TYPE_BOOLEAN);
        $record->addValue('description', null, ExportValue::TYPE_STRING);

        $result = $this->invokePrivateMethod($this->provider, 'isTableRowEmpty', [$record]);
        $this->assertTrue($result, 'Record with all empty values should be considered empty');
    }

    public function testIsTableRowEmptyWithSomeNonEmptyValues(): void
    {
        $record = new ExportRecord();
        $record->addValue('item', 'Laptop', ExportValue::TYPE_STRING);
        $record->addValue('price', 0, ExportValue::TYPE_INTEGER);
        $record->addValue('active', false, ExportValue::TYPE_BOOLEAN);

        $result = $this->invokePrivateMethod($this->provider, 'isTableRowEmpty', [$record]);
        $this->assertFalse($result, 'Record with some non-empty values should not be considered empty');
    }

    public function testIsValueEmptyOrZero(): void
    {
        $testCases = [
            // Empty values
            [null, true, 'null should be considered empty'],
            ['', true, 'empty string should be considered empty'],
            [0, true, 'integer zero should be considered empty'],
            [0.0, true, 'float zero should be considered empty'],
            [false, true, 'boolean false should be considered empty'],

            // Non-empty values
            ['test', false, 'non-empty string should not be considered empty'],
            [1, false, 'non-zero integer should not be considered empty'],
            [1.5, false, 'non-zero float should not be considered empty'],
            [true, false, 'boolean true should not be considered empty'],
            [[], false, 'empty array should not be considered empty'],
            ['0', false, 'string "0" should not be considered empty'],
        ];

        foreach ($testCases as [$value, $expected, $message]) {
            $result = $this->invokePrivateMethod($this->provider, 'isValueEmptyOrZero', [$value]);
            $this->assertEquals($expected, $result, $message . ' (value: ' . var_export($value, true) . ')');
        }
    }

    public function testCreateTableFromGroupingDataFiltersEmptyTables(): void
    {
        // Test that tables with only 1 row containing empty/zero values are filtered out
        $model = $this->createMockModel();

        $groupingData = [
            'names' => ['item', 'price', 'active'],
            'labels' => ['Item', 'Price', 'Active'],
            'hidden' => [],
            'values' => [
                ['', 0, false] // Single row with only empty/zero values
            ]
        ];

        $result = $this->invokePrivateMethod($this->provider, 'createTableFromGroupingData', [
            $model, 'empty_table', $groupingData, []
        ]);

        $this->assertNull($result, 'Table with single empty row should be filtered out');
    }

    public function testCreateTableFromGroupingDataKeepsNonEmptyTables(): void
    {
        // Test that tables with non-empty values are kept
        $model = $this->createMockModel();

        $groupingData = [
            'names' => ['item', 'price', 'active'],
            'labels' => ['Item', 'Price', 'Active'],
            'hidden' => [],
            'values' => [
                ['Laptop', 0, false] // Single row with at least one non-empty value
            ]
        ];

        $result = $this->invokePrivateMethod($this->provider, 'createTableFromGroupingData', [
            $model, 'non_empty_table', $groupingData, []
        ]);

        $this->assertInstanceOf(ExportTable::class, $result, 'Table with non-empty values should be kept');
        $this->assertEquals(1, $result->count());
    }

    public function testCreateTableFromGroupingDataKeepsMultiRowTables(): void
    {
        // Test that tables with multiple rows are kept even if all rows are empty
        $model = $this->createMockModel();

        $groupingData = [
            'names' => ['item', 'price'],
            'labels' => ['Item', 'Price'],
            'hidden' => [],
            'values' => [
                ['', 0],
                ['', 0]
            ]
        ];

        $result = $this->invokePrivateMethod($this->provider, 'createTableFromGroupingData', [
            $model, 'multi_row_table', $groupingData, []
        ]);

        $this->assertInstanceOf(ExportTable::class, $result, 'Table with multiple rows should be kept');
        $this->assertEquals(2, $result->count());
    }

    public function testGetTablesForRecordWithFilteredEmptyTables(): void
    {
        // Integration test: create a model with both empty and non-empty tables
        $varsForTemplate = [
            'empty_table' => [
                'type' => 'grouping',
                'names' => ['item', 'price'],
                'labels' => ['Item', 'Price'],
                'hidden' => [],
                'values' => [
                    ['', 0] // Single empty row
                ]
            ],
            'non_empty_table' => [
                'type' => 'grouping',
                'names' => ['product', 'cost'],
                'labels' => ['Product', 'Cost'],
                'hidden' => [],
                'values' => [
                    ['Laptop', 999.99] // Non-empty row
                ]
            ],
            'multi_row_empty_table' => [
                'type' => 'grouping',
                'names' => ['category', 'count'],
                'labels' => ['Category', 'Count'],
                'hidden' => [],
                'values' => [
                    ['', 0],
                    ['', 0]
                ]
            ]
        ];

        $model = new \Model(['id' => 123], [], [], $varsForTemplate);
        $collection = $this->provider->getTablesForRecord($model);

        // Should have 2 tables: non_empty_table and multi_row_empty_table
        // empty_table should be filtered out
        $this->assertTrue($collection->hasTables());
        $this->assertEquals(2, $collection->count());

        $tables = $collection->getTables();
        $tableTypes = array_keys($tables);

        $this->assertContains('non_empty_table', $tableTypes);
        $this->assertContains('multi_row_empty_table', $tableTypes);
        $this->assertNotContains('empty_table', $tableTypes);
    }
}
