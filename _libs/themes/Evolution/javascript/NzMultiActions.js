'use strict';

class NzMultiActions {
    toolbarEl;
    multiBar;
    multiForm;
    visibleWrapperEl;
    extendedWrapperEl;
    extendedListEl;
    extendButton;
    extendButtonWidth = 24;
    buttonList;
    buttonsComulativeWidth;

    placeholderEl;
    contentLoader;
    baseEndpoint;
    grid;
    lastType = [];
    lastTypeSection = null;
    updated = false;

    onLoadBound;

    constructor(placeholderEl, grid) {
        this.grid = grid;
        this.onLoadBound = this.onLoad.bind(this);
        this.placeholderEl = placeholderEl;
        this.placeholderEl.addEventListener('click', (e) => {
            if (! e.target.closest('.nz-extended-button')) {return;}
            this.multiBar.classList.toggle('nz--opened');
        });

        this.baseEndpoint = placeholderEl.dataset.endpoint;
        this.contentLoader = new NzContentLoader(this.baseEndpoint, placeholderEl);

        const fixExtendedMenuBounded = this.fixExtendedMenu.bind(this);
        let resizeTimer = 0;
        window.addEventListener('resize', () => {
            if (! this.updated) {return;}
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(fixExtendedMenuBounded, 100);
        });
    }

    static init(placeholderEl, grid) {
        return new NzMultiActions(placeholderEl, grid);
    }

    update() {
        let typeSectionIsDifferent = this.grid.typeSection !== this.lastTypeSection;
        let typeIsDifferent = false;

        if (this.grid.type.length !== this.lastType.length) {
            typeIsDifferent = true;
        } else {
            for (const x of this.grid.type) {
                if (this.lastType.indexOf(x) === -1) {
                    typeIsDifferent = true;
                    break;
                }
            }
        }

        if (this.updated && !(typeIsDifferent || typeSectionIsDifferent)) {
            return;
        }

        this.lastType = this.grid.type;
        this.lastTypeSection = this.grid.typeSection;

        this.placeholderEl.innerHTML = '';

        let endpoint = this.baseEndpoint;

        if (this.grid.type.length === 1) {
            endpoint += endpoint.indexOf('?') === -1 ? '?' : '&';
            let type = this.grid.type[0];
            endpoint += `type=${type}`;
        }

        if (this.grid.typeSection) {
            endpoint += endpoint.indexOf('?') === -1 ? '?' : '&';
            let typeSection = this.grid.typeSection ? this.grid.typeSection : '';
            endpoint += `type_section=${typeSection}`;
        }
        this.contentLoader.setEndpoint(endpoint);
        this.contentLoader.setLoaded(false);
        this.contentLoader.load()
            .then(this.onLoadBound);
    }

    onLoad(contentEl, contetntText) {
        this.updated = true;
        this.multiBar = contentEl.querySelector('.nz-grid-toobar .nz-grid-multi-actions');
        if (this.multiBar === null) {
            return null;
        }

        this.multiForm = this.multiBar.closest('form');
        if (this.multiForm === null) {
            return null;
        }

        this.multiBar.addEventListener('click', this.multiActionBarClick.bind(this));
        this.multiForm.addEventListener('submit', this.multiActionFormSubmit.bind(this));

        this.toolbarEl = document.querySelector('.nz-grid-toobar');
        this.visibleWrapperEl = this.multiBar.querySelector('.nz-visible');
        this.extendedWrapperEl = this.multiBar.querySelector('.nz-extended');
        this.extendedListEl = this.extendedWrapperEl.querySelector('.nz-extended-list');
        this.extendButton = this.multiBar.querySelector('.nz-extended-button');
        this.buttonList = this.multiBar.querySelectorAll('.nz-button--multi');

        this.buttonsComulativeWidth = 0;
        this.buttonList.forEach((button) => {
            const s = window.getComputedStyle(button, null);
            const fullWidth = button.offsetWidth + parseInt(s.getPropertyValue('margin-left')) + parseInt(s.getPropertyValue('margin-right'));
            button.dataset.fullWidth = fullWidth;
            this.buttonsComulativeWidth += fullWidth;

        });

        setTimeout(() => {
            this.fixExtendedMenu();
            this.prepComplexButtons();
        }, 20);
    }

    multiActionButtonClick(e, multiBar) {
        e.preventDefault();

        const action = this.dataset.action;
        if (action === 'emptyexport') {
            nzShowLoading();
            const url = new URL(env.base_host + env.base_url);
            url.searchParams.set(env.module_param, env.module_name);
            url.searchParams.set(env.module_name, 'export');
            url.searchParams.set('file_name', this.closest('form').querySelector('[name="file_name"]').value);
            url.searchParams.set('format', 'xlsx');
            url.searchParams.set('group_tables', '0');
            url.searchParams.set('export_what', 'empty');
            url.searchParams.set('session_param', );
            url.searchParams.set('export_previous_action', this.closest('form').querySelector('[name="export_previous_action"]').value);
            url.searchParams.set('disable_preload', '1');
            url.searchParams.set('type_id', multiBar.grid.type[0]);
            url.searchParams.set('type_section', multiBar.grid.typeSection);

            const formData = new FormData();
            formData.append(this.name, this.value);
            formData.append('format', 'xlsx');
            formData.append('group_tables', '0');
            formData.append('export_what', 'empty');
            formData.append('session_param', this.closest('form').querySelector('[name="session_param"]').value);

            const promise = fetch(url, {
                method: 'post',
                body: formData,
            }).finally(() => {
                nzHideLoading();
                document.body.click();
            });

            multiBar.downloadFile(promise);

            return;
        }

        if (document.querySelector('.nz-grid-multi-actions.nz--active') === null) {
            Nz.alert(i18n.messages.no_select_records, i18n.messages['alert_' + action]);
            return;
        }
        if (action === 'multistatus') {
            const parent = this.closest('.nz-popout-body');
            const requireComment = !!parent.querySelector('#multistatusSelect option.requires_comment:checked');
            const comment = parent.querySelector('#multistatus_comment').value;

            if (requireComment && comment.length === 0) {
                Nz.alert(
                    i18n._.required_statuses_option_requires_comment,
                    i18n.messages['error_status_requires_comment']
                );
                return;
            }
        }

        // Export is passive and doesn't require confirmation
        if (action === 'export') {
            e.target.form.requestSubmit(e.target);
            return;
        }

        const that = e.target;
        Nz.confirm(i18n._[action], i18n.messages['confirm_' + action])
            .then(() => {
                // request submit activates the form submit event
                // (the simple submit() will not fire event and will not activate the handlers)
                that.form.requestSubmit(that);
            })
            .catch(() => {});
    }

    downloadFile(promise) {
        let disposition, filename;
        promise.then(response => {
            disposition = response.headers.get('Content-Disposition');
            filename = disposition.match(/filename="(.*)"/)[1];
            return response.blob();
        })
            .then(blob => {
                this.grid.selectionClear();
                Nz.download(window.URL.createObjectURL(blob), filename);
            })
    }

    multiActionBarClick(e) {
        if (e.target.closest('.nz-button-multi-confirm')) {
            this.multiActionButtonClick.apply(e.target, [e, this]);
        }
        if (e.target.closest('.nz-form-button')) {
            this.multiActionButtonClick.apply(e.target, [e, this]);
        }
    }

    multiActionFormSubmit(e) {
        let pressedButton = e.submitter;

        // Prevent if button not active
        if (!pressedButton || !pressedButton.closest('.nz-grid-multi-actions.nz--active')) {
            e.preventDefault();
            return;
        }

        const nzAction = pressedButton.dataset.action;
        const nzMode = pressedButton.dataset.mode ? pressedButton.dataset.mode : 'refresh';

        nzShowLoading();
        const formEl = e.target.closest('form');
        let url = formEl.action;
        url += url.indexOf('?') === -1 ? '?' : '&';
        url += `${env.controller_name}=${nzAction}`;

        if (nzMode === 'refresh' || nzMode === 'download') {
            e.preventDefault();

            const formData = new FormData(formEl);
            for (const d of this.grid.getSelectedIds()) {
                formData.append('items[]', d);
                formData.append(pressedButton.name, pressedButton.value);
            }

            const promise = fetch(url, {
                method: 'post',
                body: formData,
            }).finally(() => {
                nzHideLoading();
                removeResubmitPrevention(this.multiForm);
                const popout = pressedButton.closest('.nz-popout-panel');
                if (popout) {
                    document.body.click();
                }
            });
            if (nzMode === 'refresh') {
                let status;
                promise.then(response => {
                    status = response.status;
                    return response.json();
                })
                    .then(data => {
                        if (data.errors || data.warnings || data.messages) {
                            MainMessages.getInstance().setMessages(MainMessages.converStructuredMsgArray2Plain(data));
                        }

                        if (status === 200) {
                            this.grid.selectionClear();
                            this.grid.grid.refresh();
                        }
                    })
                    .catch(e => {
                        this.grid.selectionClear();
                        this.grid.grid.refresh();
                        // Multi action doesn't support Ajax, but it's OK
                    });
            } else if (nzMode === 'download') {
                this.downloadFile(promise);
            }
        } else if (nzMode === 'submit') {
            // continue to submit
            const el_proto = document.createElement('input')
            el_proto.type = 'hidden';
            el_proto.name = 'items[]';

            const buttValueEl = el_proto.cloneNode();
            buttValueEl.name = env.controller_name;
            buttValueEl.value = nzAction;
            formEl.append(buttValueEl);

            for (const id of this.grid.getSelectedIds()) {
                const el = el_proto.cloneNode();
                el.value = id;
                formEl.append(el);
            }
        }
    }

    prepComplexButtons() {
        // Multistatus
        this.prepMultiPopout('#multistatusSelect', '[name="multistatusButton"]');

        // Multiprint
        this.prepMultiPopout('#pattern', '#multiprintButton');

        // Multitag
        this.prepMultiPopout('#tagsSelect', '.multitags-submit-button');
    }

    prepMultiPopout(toggleSelector, buttonSelector) {
        const multiToggle = this.multiBar.querySelector(toggleSelector);
        if(!multiToggle) {
            return;
        }
        const multiButton = this.multiBar.querySelectorAll(buttonSelector);
        multiToggle.addEventListener('change', (e) => {
            if (!e.target.value) {
                multiButton.forEach(el => el.disabled = true);
            } else {
                multiButton.forEach(el => el.disabled = false);
            }
        });
        multiToggle.dispatchEvent(new Event('change', {target: multiToggle}));
    }

    fixExtendedMenu() {
        const availableButtonWidth = this.multiBar.clientWidth - 21;
        const refWidth = availableButtonWidth - this.extendButtonWidth;

        if (this.buttonsComulativeWidth >= availableButtonWidth) {
            let visibleWidth = this.extendButtonWidth;
            this.buttonList.forEach((button) => {
                const buttonWidth = parseInt(button.dataset.fullWidth);
                if (buttonWidth + visibleWidth < refWidth) {
                    this.visibleWrapperEl.appendChild(button);
                    if (typeof button.__ez_instance !== 'undefined' && typeof button.__ez_instance[0]) {
                        this.visibleWrapperEl.appendChild(button.__ez_instance[0].popoutEl);
                        button.__ez_instance[0].popoutElParent = this.visibleWrapperEl;
                    }
                    visibleWidth += button.offsetWidth;
                } else {
                    this.extendedListEl.appendChild(button);
                    if (typeof button.__ez_instance !== 'undefined' && typeof button.__ez_instance[0]) {
                        this.extendedListEl.appendChild(button.__ez_instance[0].popoutEl);
                        button.__ez_instance[0].popoutElParent = this.extendedListEl;
                    }
                }
            });

            this.multiBar.classList.add('nz--extended');
            this.visibleWrapperEl.appendChild(this.extendButton);
            if (window.innerWidth > 800) {
                this.extendedWrapperEl.style.right = (
                    this.extendButton.parentElement.offsetWidth
                    - this.extendButton.offsetLeft
                    - this.extendButtonWidth
                ) + 'px';
            }
        } else {
            if (this.extendedListEl.children.length > 1) {
                Array.from(this.extendedListEl.children).forEach(el => {
                    if (!el.matches('.nz-button--multi')) {return;}
                    this.visibleWrapperEl.appendChild(el);
                    if (typeof el.__ez_instance !== 'undefined' && typeof el.__ez_instance[0]) {
                        this.visibleWrapperEl.appendChild(el.__ez_instance[0].popoutEl);
                        el.__ez_instance[0].popoutElParent = this.visibleWrapperEl;
                    }
                });
            }
            this.extendedWrapperEl.appendChild(this.extendButton);
            this.multiBar.classList.remove('nz--extended');
        }
    }
}
