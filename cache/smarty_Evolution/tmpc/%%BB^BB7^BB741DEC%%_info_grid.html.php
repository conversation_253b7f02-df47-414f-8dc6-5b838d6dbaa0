<?php /* Smarty version 2.6.33, created on 2025-06-23 20:03:33
         compiled from /var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_info_grid.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/_info_grid.html', 4, false),)), $this); ?>
<?php echo '
<div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ' ${properties.name} ${if(!properties.is_company)} ${properties.lastname}${/if}</div>
<div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ' ${properties.type_name} (${if(properties.is_company)}'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '${else}'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '${/if})</div>
<div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${Nz.formatDate(properties.added)}<br>
  '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.added_by_name}</div>
<div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${Nz.formatDate(properties.modified)}<br>
  '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.modified_by_name}</div>
${if(properties.deleted_by_name)}
<div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${Nz.formatDate(properties.deleted)}<br>
  '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.deleted_by_name}</div>
${/if}
<div>
  <strong>'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ':</strong>  &nbsp;
  <span class="translations">
    ${for(trans of properties.translations)}
      <span ${if(trans==properties.model_lang)} class="selected"${/if} title="${trans}">${trans}</span>
      ${if(transIndex!=properties.translations.length-1)}
        &nbsp; | &nbsp;
      ${/if}
    ${/for}
  </span>
</div>
'; ?>
