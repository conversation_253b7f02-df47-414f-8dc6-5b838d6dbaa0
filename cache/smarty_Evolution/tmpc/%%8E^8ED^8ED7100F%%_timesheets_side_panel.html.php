<?php /* Smarty version 2.6.33, created on 2025-06-23 20:30:38
         compiled from _timesheets_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_timesheets_side_panel.html', 11, false),array('modifier', 'default', '_timesheets_side_panel.html', 17, false),array('modifier', 'date_format', '_timesheets_side_panel.html', 17, false),array('modifier', 'mb_lower', '_timesheets_side_panel.html', 17, false),array('modifier', 'mb_truncate', '_timesheets_side_panel.html', 28, false),array('modifier', 'url2href', '_timesheets_side_panel.html', 28, false),array('modifier', 'nl2br', '_timesheets_side_panel.html', 28, false),array('function', 'cycle', '_timesheets_side_panel.html', 15, false),array('function', 'help', '_timesheets_side_panel.html', 17, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<?php echo ''; ?><?php if ($this->_tpl_vars['module'] && $this->_tpl_vars['controller']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['controller'] != $this->_tpl_vars['module']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('controller_action_string', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>


<input type="hidden" id="<?php echo $this->_tpl_vars['side_panel']; ?>
_total" name="<?php echo $this->_tpl_vars['side_panel']; ?>
_total" class="total" value="<?php echo $this->_tpl_vars['total']; ?>
" />
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_content'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption" nowrap="nowrap" style="width: 60px;"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_duration'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
/<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_duration_billing_short'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
<?php $_from = $this->_tpl_vars['timesheets']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['item']):
        $this->_foreach['i']['iteration']++;
?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 vtop">
    <td class="t_border">
      <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['tasks_timesheets_completed_by']), $this);?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['item']->get('user_id_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "-") : smarty_modifier_default($_tmp, "-")); ?>
 (<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['item']->get('startperiod'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
 <?php echo ((is_array($_tmp=$this->_config[0]['vars']['to'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['item']->get('endperiod'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
)<br />
      <?php if ($this->_tpl_vars['item']->get('subject')): ?><strong><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('subject'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><br /><?php endif; ?>
      <?php echo ''; ?><?php ob_start(); ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/full_comment.png" width="12" height="12" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['full_content'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['full_content'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" onclick="toggleContent(\'content\', '; ?><?php echo $this->_foreach['i']['iteration']; ?><?php echo ');" class="pointer" />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('content_full', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/full_comment.png" width="12" height="12" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['part_content'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['part_content'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" onclick="toggleContent(\'content\', '; ?><?php echo $this->_foreach['i']['iteration']; ?><?php echo ');" class="pointer" />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('content_part', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <div id="content_part_<?php echo $this->_foreach['i']['iteration']; ?>
">
        <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['item']->get('content'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 130, $this->_tpl_vars['content_full']) : smarty_modifier_mb_truncate($_tmp, 130, $this->_tpl_vars['content_full'])))) ? $this->_run_mod_handler('url2href', true, $_tmp, 35) : smarty_modifier_url2href($_tmp, 35)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?>

      </div>
      <div id="content_full_<?php echo $this->_foreach['i']['iteration']; ?>
" style="display: none;">
        <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['item']->get('content'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp, 35) : smarty_modifier_url2href($_tmp, 35)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?>
<?php echo $this->_tpl_vars['content_part']; ?>

      </div>
    </td>
    <td class="hright"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['item']->get('duration'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "-") : smarty_modifier_default($_tmp, "-")); ?>
/<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['item']->get('duration_billing'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "-") : smarty_modifier_default($_tmp, "-")); ?>
</td>
  </tr>
<?php endforeach; else: ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
    <td class="error" colspan="2"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
  </tr>
<?php endif; unset($_from); ?>
</table>
<?php endif; ?>