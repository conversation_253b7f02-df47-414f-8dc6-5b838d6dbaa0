<?php /* Smarty version 2.6.33, created on 2025-06-23 20:30:38
         compiled from /var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_assignments_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_assignments_side_panel.html', 5, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_assignments_side_panel.html', 22, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_assignments_side_panel.html', 18, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_assignments_side_panel.html', 19, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <?php if (in_array ( 'owner' , $this->_tpl_vars['settings_assign'] )): ?>
      <td class="t_panel_caption" nowrap="nowrap" style="width: 25%"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_assign_owner'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php endif; ?>
    <?php if (in_array ( 'responsible' , $this->_tpl_vars['settings_assign'] )): ?>
      <td class="t_panel_caption" nowrap="nowrap" style="width: 25%"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_assign_responsible'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php endif; ?>
    <?php if (in_array ( 'observer' , $this->_tpl_vars['settings_assign'] )): ?>
      <td class="t_panel_caption" nowrap="nowrap" style="width: 25%"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_assign_observer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php endif; ?>
    <?php if (in_array ( 'decision' , $this->_tpl_vars['settings_assign'] )): ?>
      <td class="t_panel_caption" nowrap="nowrap" style="width: 25%"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_assign_decision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php endif; ?>
  </tr>
  <?php if ($this->_tpl_vars['model']->get('assignments_owner') || $this->_tpl_vars['model']->get('assignments_responsible') || $this->_tpl_vars['model']->get('assignments_observer') || $this->_tpl_vars['model']->get('assignments_decision')): ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
    <?php echo smarty_function_counter(array('name' => 'num_columns','assign' => 'num_columns','start' => 0), $this);?>

    <?php if (in_array ( 'owner' , $this->_tpl_vars['settings_assign'] )): ?>
      <?php echo smarty_function_counter(array('name' => 'num_columns','print' => false), $this);?>

      <td class="<?php if ($this->_tpl_vars['num_columns'] < count($this->_tpl_vars['settings_assign'])): ?>t_border <?php endif; ?>vtop">
      <?php $_from = $this->_tpl_vars['model']->get('assignments_owner'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assignment']):
        $this->_foreach['i']['iteration']++;
?>
        <?php if ($this->_tpl_vars['assignment']['is_portal']): ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user_portal.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['portal_user']; ?>
" />
        <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['normal_user']; ?>
" />
        <?php endif; ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['assignment']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
      <?php endforeach; else: ?>
        &nbsp;
      <?php endif; unset($_from); ?>
      </td>
    <?php endif; ?>
    <?php if (in_array ( 'responsible' , $this->_tpl_vars['settings_assign'] )): ?>
      <?php echo smarty_function_counter(array('name' => 'num_columns','print' => false), $this);?>

      <td class="<?php if ($this->_tpl_vars['num_columns'] < count($this->_tpl_vars['settings_assign'])): ?>t_border <?php endif; ?>vtop">
      <?php $_from = $this->_tpl_vars['model']->get('assignments_responsible'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assignment']):
        $this->_foreach['j']['iteration']++;
?>
        <?php if ($this->_tpl_vars['assignment']['is_portal']): ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user_portal.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['portal_user']; ?>
" />
        <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['normal_user']; ?>
" />
        <?php endif; ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['assignment']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
      <?php endforeach; else: ?>
        &nbsp;
      <?php endif; unset($_from); ?>
      </td>
    <?php endif; ?>
    <?php if (in_array ( 'observer' , $this->_tpl_vars['settings_assign'] )): ?>
      <?php echo smarty_function_counter(array('name' => 'num_columns','print' => false), $this);?>

      <td class="<?php if ($this->_tpl_vars['num_columns'] < count($this->_tpl_vars['settings_assign'])): ?>t_border <?php endif; ?>vtop">
      <?php $_from = $this->_tpl_vars['model']->get('assignments_observer'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['k'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['k']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assignment']):
        $this->_foreach['k']['iteration']++;
?>
        <?php if ($this->_tpl_vars['assignment']['is_portal']): ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user_portal.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['portal_user']; ?>
" />
        <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['normal_user']; ?>
" />
        <?php endif; ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['assignment']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
      <?php endforeach; else: ?>
        &nbsp;
      <?php endif; unset($_from); ?>
      </td>
    <?php endif; ?>
    <?php if (in_array ( 'decision' , $this->_tpl_vars['settings_assign'] )): ?>
      <td class="vtop">
      <?php $_from = $this->_tpl_vars['model']->get('assignments_decision'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['l'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['l']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['assignment']):
        $this->_foreach['l']['iteration']++;
?>
        <?php if ($this->_tpl_vars['assignment']['is_portal']): ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user_portal.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['portal_user']; ?>
" />
        <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['normal_user']; ?>
" />
        <?php endif; ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['assignment']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
      <?php endforeach; else: ?>
        &nbsp;
      <?php endif; unset($_from); ?>
      </td>
    <?php endif; ?>
   </tr>
   <?php else: ?>
   <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
     <td class="error" colspan="<?php echo count($this->_tpl_vars['settings_assign']); ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
   </tr>
   <?php endif; ?>
</table>
<?php endif; ?>